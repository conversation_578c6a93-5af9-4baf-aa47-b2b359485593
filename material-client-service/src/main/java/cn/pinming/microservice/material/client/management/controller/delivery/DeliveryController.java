package cn.pinming.microservice.material.client.management.controller.delivery;


import cn.pinming.microservice.material.client.management.infrastructure.response.SingleResponse;
import cn.pinming.microservice.material.client.management.common.form.DeliveryAddForm;
import cn.pinming.microservice.material.client.management.common.query.DeliveryPageQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryInitVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryTruckVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryVO;
import cn.pinming.microservice.material.client.management.service.biz.IDeliveryService;
import cn.pinming.microservice.material.client.management.service.biz.IPurchaseOrderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 发货单(运单) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Api(value = "运单管理", tags = {"delivery"})
@RestController
@RequestMapping("/api/delivery")
public class DeliveryController {

    @Resource
    private IPurchaseOrderService purchaseOrderService;
    @Resource
    private IDeliveryService deliveryService;

    @ApiOperation(value = "初始化发货单", responseReference = "SingleResponse«DeliveryInitVO»", nickname = "deliveryInit")
    @GetMapping("/init")
    public SingleResponse<DeliveryInitVO> init(@RequestParam(required = false) Long purchaseOrderId) {
        DeliveryInitVO result = purchaseOrderService.initDelivery(purchaseOrderId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "发货车牌号列表(默认100条)", responseReference = "SingleResponse«List<DeliveryTruckVO>»", nickname = "truckList")
    @GetMapping("/truck/list")
    public SingleResponse<List<DeliveryTruckVO>> truckList() {
        List<DeliveryTruckVO> list = deliveryService.selectTruckList();
        return SingleResponse.of(list);
    }

    @ApiOperation(value = "保存发货单(运单)", responseReference = "SingleResponse«Long»", nickname = "deliveryAdd")
    @PostMapping("/add")
    public SingleResponse<Long> add(@Validated @RequestBody DeliveryAddForm form) {
        Long deliveryId = deliveryService.add(form);
        return SingleResponse.of(deliveryId);
    }

    @ApiOperation(value = "发货单列表", responseReference = "SingleResponse«Page<DeliveryVO>»", nickname = "deliveryPage")
    @PostMapping("/page")
    public SingleResponse<Page<DeliveryVO>> page(@RequestBody DeliveryPageQuery query) {
        Page<DeliveryVO> page = deliveryService.pageByQuery(query);
        return SingleResponse.of(page);
    }

    @ApiOperation(value = "发货单(作废)", responseReference = "SingleResponse«?»", nickname = "deliveryCancel")
    @DeleteMapping("/{deliveryId}")
    public SingleResponse<?> cancel(@PathVariable Long deliveryId) {
        deliveryService.cancel(deliveryId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "发货单详情(整车发货单数据)", responseReference = "SingleResponse«DeliveryDetailVO»", nickname = "deliveryDetail")
    @GetMapping("/{deliveryId}")
    public SingleResponse<DeliveryDetailDTO> detail(@PathVariable Long deliveryId) {
        DeliveryDetailDTO result = deliveryService.detailByDeliveryId(deliveryId);
        return SingleResponse.of(result);
    }

    @ApiOperation(value = "发送运单短信通知", nickname = "deliverySms")
    @GetMapping("/sms/{deliveryId}")
    public SingleResponse<?> deliverySms(@PathVariable Long deliveryId) {
        deliveryService.sendDeliverySms(deliveryId);
        return SingleResponse.buildSuccess();
    }

    @ApiOperation(value = "发货单列表导出", nickname = "deliveryListExport")
    @PostMapping("/listExport")
    public void listExport(@RequestBody DeliveryPageQuery query, HttpServletResponse response) {
        deliveryService.listExport(query, response);
    }

    @ApiOperation(value = "修改司机手机号", nickname = "updateDriverMobile")
    @PutMapping("/{deliveryId}/{mobile}/mobile")
    public SingleResponse<?> updateDriverMobile(@PathVariable Long deliveryId, @PathVariable String mobile) {
        deliveryService.updateDriverMobile(deliveryId, mobile);
        return SingleResponse.buildSuccess();
    }

}

