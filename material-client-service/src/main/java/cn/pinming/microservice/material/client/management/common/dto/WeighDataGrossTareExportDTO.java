package cn.pinming.microservice.material.client.management.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仅毛皮重称重记录导出DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class WeighDataGrossTareExportDTO {

    @ExcelProperty("确认单号")
    private String confirmNo;

    @ExcelProperty("归属方名称")
    private String attributionName;

    @ExcelProperty("设备机器码")
    private String deviceSn;

    @ExcelProperty("车牌号")
    private String truckNo;

    @ExcelProperty("供应商名称")
    private String supplierName;

    @ExcelProperty("司机")
    private String driver;

    @ExcelProperty("材料名称")
    private String material;

    @ExcelProperty("实重")
    private BigDecimal weightActual;

    @ExcelProperty("实际数量")
    private BigDecimal actualCount;

    @ExcelProperty("结算单位")
    private String weightUnit;

    @ExcelProperty("称重类型")
    private String weighTypeStr;

    @ExcelProperty("毛重")
    private BigDecimal weightGross;

    @ExcelProperty("皮重")
    private BigDecimal weightTare;

    @ExcelProperty("扣重")
    private BigDecimal weightDeduct;

    @ExcelProperty("含水率")
    private BigDecimal moistureContent;

    @ExcelProperty("换算系数")
    private BigDecimal ratio;

    @ExcelProperty("发货数量")
    private BigDecimal weightSend;

    @ExcelProperty("偏差量")
    private BigDecimal deviationCount;

    @ExcelProperty("偏差率")
    private BigDecimal deviationRate;

    @ExcelProperty("进场时间")
    private String enterTimeStr;

    @ExcelProperty("出场时间")
    private String leaveTimeStr;

    @ExcelProperty("推送状态")
    private String pushStateStr;

    @ExcelProperty("创建时间")
    private String gmtCreateStr;

}
