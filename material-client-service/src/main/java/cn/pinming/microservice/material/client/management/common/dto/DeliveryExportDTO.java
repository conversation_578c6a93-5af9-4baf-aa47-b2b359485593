package cn.pinming.microservice.material.client.management.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 发货单导出DTO
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
public class DeliveryExportDTO {

    @ExcelProperty("发货单号")
    private String no;

    @ExcelProperty("采购方")
    private String project;

    @ExcelProperty("供应商")
    private String supplierName;

    @ExcelProperty("供应商账户名称")
    private String accountName;

    @ExcelProperty("车牌号")
    private String truckNo;

    @ExcelProperty("司机")
    private String driver;

    @ExcelProperty("车载货物")
    private String materialCategory;

    @ExcelProperty("状态")
    private String statusStr;

    @ExcelProperty("推送状态")
    private String pushStateStr;

    @ExcelProperty("创建时间")
    private String gmtCreateStr;

}
