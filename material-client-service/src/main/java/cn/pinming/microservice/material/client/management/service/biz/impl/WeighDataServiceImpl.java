package cn.pinming.microservice.material.client.management.service.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.pinming.microservice.base.common.util.ExcelUtil;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataGrossTareExportDTO;
import cn.pinming.material.v2.model.PageList;
import cn.pinming.material.v2.model.query.WeighDataQuery;
import cn.pinming.microservice.material.client.management.common.dto.ExpirePicFixDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataCheckDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataPullDTO;
import cn.pinming.microservice.material.client.management.common.dto.WeighDataSavedDTO;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperAppEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeveloperTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceIsUsedEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceReceiveEnum;
import cn.pinming.microservice.material.client.management.common.enums.DeviceTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.PushStatusEnum;
import cn.pinming.microservice.material.client.management.common.enums.RiskGradeEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataTypeEnum;
import cn.pinming.microservice.material.client.management.common.enums.WeighDataUpdateTypeEnum;
import cn.pinming.microservice.material.client.management.common.form.ExpirePicFixForm;
import cn.pinming.microservice.material.client.management.common.form.RefreshPicForm;
import cn.pinming.microservice.material.client.management.common.form.WeighDataStandardForm;
import cn.pinming.microservice.material.client.management.common.form.WeighPicStandardForm;
import cn.pinming.microservice.material.client.management.common.mapper.WeighDataMapper;
import cn.pinming.microservice.material.client.management.common.mapper.WeighDataUpdateLogMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.DeviceExtMapper;
import cn.pinming.microservice.material.client.management.common.mapper.ext.WeighDataExtMapper;
import cn.pinming.microservice.material.client.management.common.model.UserDO;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveAlarmDO;
import cn.pinming.microservice.material.client.management.common.model.WeighCurveDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataPicDO;
import cn.pinming.microservice.material.client.management.common.model.WeighDataUpdateLogDO;
import cn.pinming.microservice.material.client.management.common.query.WeighDataLocalQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighDataPageQuery;
import cn.pinming.microservice.material.client.management.common.query.WeighPullQuery;
import cn.pinming.microservice.material.client.management.common.vo.DataSyncVO;
import cn.pinming.microservice.material.client.management.common.vo.RefreshPicVO;
import cn.pinming.microservice.material.client.management.common.vo.UserVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataCurvePullVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataDetailVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPicVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataPullVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataSimpleVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataUpdateLogVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataVO;
import cn.pinming.microservice.material.client.management.common.vo.WeighDataConfirmVO;
import cn.pinming.microservice.material.client.management.infrastructure.constant.ConfigConstant;
import cn.pinming.microservice.material.client.management.infrastructure.constant.SdkQueryConstant;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizErrorException;
import cn.pinming.microservice.material.client.management.infrastructure.exception.BizExceptionMessageEnum;
import cn.pinming.microservice.material.client.management.infrastructure.util.UserIdUtil;
import cn.pinming.microservice.material.client.management.service.biz.AppInvocationDailyLogService;
import cn.pinming.microservice.material.client.management.service.biz.FileOssService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveAlarmService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighCurveService;
import cn.pinming.microservice.material.client.management.service.biz.PlateIdentifyService;
import cn.pinming.microservice.material.client.management.service.biz.UserConfigService;
import cn.pinming.microservice.material.client.management.service.biz.UserService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataPicService;
import cn.pinming.microservice.material.client.management.service.biz.WeighDataService;
import cn.pinming.microservice.material.client.management.service.biz.IWeighDataConfirmService;
import cn.pinming.microservice.material.client.management.service.business.WeighDataPushBusinessService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.FileCenterService;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.DownloadUrlOptionsDto;
import cn.pinming.zhjz.cxptz.microservice.filecenter.api.model.FileIdentityDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.stream.Collectors;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WeighDataServiceImpl extends ServiceImpl<WeighDataMapper, WeighDataDO> implements WeighDataService {
    @DubboReference
    private FileCenterService            fileCenterService;
    @Resource
    private DeviceExtMapper              deviceExtMapper;
    @Resource
    private WeighDataService             weighDataService;
    @Resource
    private WeighDataExtMapper           weighDataExtMapper;
    @Resource
    private WeighDataPicService          weighDataPicService;
    @Resource
    private FileOssService               fileOssService;
    @Resource
    private UserIdUtil                   userIdUtil;
    @Resource
    private AppInvocationDailyLogService appInvocationDailyLogService;
    @Resource
    private WeighDataUpdateLogMapper     weighDataUpdateLogMapper;
    @Resource
    private UserService                  userService;
    @Resource
    private PlateIdentifyService         plateIdentifyService;
    @Resource
    private WeighDataPushBusinessService weighDataPushBusinessService;
    @Resource
    private UserConfigService            userConfigService;
    @Resource
    private IWeighCurveService           weighCurveService;
    @Resource
    private WeighCurveServiceImpl        weighCurveServiceImpl;
    @Resource
    private IWeighDataConfirmService     weighDataConfirmService;
    @Resource
    private IWeighCurveAlarmService      weighCurveAlarmService;

    @Override
    public void upload(WeighDataStandardForm bean, String deviceType) {
        String deviceSn = bean.getDeviceSn();
        WeighDataCheckDTO dto = check(deviceSn, DeveloperAppEnum.UPLOAD.value(), false, StrUtil.isNotBlank(deviceType) ? deviceType : DeviceTypeEnum.WEIGH.name());
        WeighDataDO dbDataDO = dataCheck(bean);

        WeighDataDO weighDataDO = new WeighDataDO();
        log.info("排查：{}", bean);
        boolean flag = (StrUtil.isNotBlank(bean.getTruckNo()) && StrUtil.isNotBlank(bean.getLprTruckNo()) && !bean.getTruckNo().equals(bean.getLprTruckNo()))
                || StrUtil.isBlank(bean.getLprTruckNo());
        if (ObjectUtil.isNull(dbDataDO)) {
            BeanUtils.copyProperties(bean, weighDataDO);
            weighDataDO.setAttributionId(dto.getAttributionId());
            weighDataDO.setUid(dto.getUid());
            weighDataDO.setAuxiliaryCode(dto.getAuxiliaryCode());
            if (bean.getType().equals(WeighDataTypeEnum.FIRST.value())) {
                weighDataDO.setRiskGrade(RiskGradeEnum.LOW.name());
                if (flag) {
                    weighDataDO.setRiskGrade(RiskGradeEnum.MIDDLE.name());
                    plateIdentifyService.addPlateIdentifyTask(weighDataDO.getRecordId());
                }
            }
            weighDataService.save(weighDataDO);
            // 记录调用次数
            appInvocationDailyLogService.saveInvokeLog(DeveloperAppEnum.UPLOAD.value(), dto.getUid(), -1L, 1);
            // 异步推送数据
            weighDataPushBusinessService.syncPushWeighData(weighDataDO);
        }
    }

    private WeighDataDO dataCheck(WeighDataStandardForm bean) {
        if (bean.getType().equals(WeighDataTypeEnum.FIRST.value()) && StrUtil.isBlank(bean.getTruckNo())) {
            throw new BizErrorException(BizExceptionMessageEnum.TRUCK_NO_EMPTY);
        }
        return weighDataService.lambdaQuery()
                .eq(WeighDataDO::getRecordId, bean.getRecordId())
                .one();
    }

    @Override
    public IPage<WeighDataVO> col(WeighDataPageQuery query) {
        String uId = userIdUtil.getUId();
        IPage<WeighDataVO> page = weighDataExtMapper.select(uId, query);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighDataVO> list = page.getRecords();
            list.forEach(e -> e.setTypeStr(WeighDataTypeEnum.desc(e.getType())));
        }
        return page;
    }

    @Override
    public WeighDataDetailVO detail(String recordId) {
//        String uId = userIdUtil.getUId();
        WeighDataDetailVO weighDataDetailVO = weighDataExtMapper.getByRecordId(recordId);
        if (weighDataDetailVO == null) {
            throw new BizErrorException(BizExceptionMessageEnum.WEIDATE_IS_NO_EXIST);
        }
        weighDataDetailVO.setTypeStr(weighDataDetailVO.getType() == null ? null : WeighDataTypeEnum.desc(weighDataDetailVO.getType()));
        weighDataDetailVO.setPicList(this.picList(weighDataDetailVO.getRecordId()));
//        List<WeighCurveDO> curveList = weighCurveService.lambdaQuery().eq(WeighCurveDO::getRecordId, recordId).list();
//        if (CollUtil.isNotEmpty(curveList)) {

//            String weighCurveId = curveDO.getWeighCurveId();
        WeighCurveAlarmDO curveAlarmDO = weighCurveAlarmService.lambdaQuery().eq(WeighCurveAlarmDO::getRecordId, recordId).one();
        weighDataDetailVO.setCurveAlarm(curveAlarmDO);
//        }
        return weighDataDetailVO;
    }

    @Override
    public List<WeighDataDetailVO> detailList(List<String> recordIdList) {
        if (CollUtil.isEmpty(recordIdList)) {
            return Collections.emptyList();
        }
        List<WeighDataDetailVO> list = weighDataExtMapper.listByRecordId(recordIdList.stream().distinct().limit(10).collect(Collectors.toList()));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        list.forEach(weighDataDetailVO -> {
            weighDataDetailVO.setTypeStr(weighDataDetailVO.getType() == null ? null : WeighDataTypeEnum.desc(weighDataDetailVO.getType()));
            weighDataDetailVO.setPicList(this.picList(weighDataDetailVO.getRecordId()));
        });
        return list;
    }

    @Override
    public void pic(WeighPicStandardForm form, String deviceType) {
        WeighDataCheckDTO dto = check(form.getDeviceSn(), DeveloperAppEnum.UPLOAD.value(), false, StrUtil.isNotBlank(deviceType) ? deviceType : DeviceTypeEnum.WEIGH.name());
        List<WeighDataPicDO> list = weighDataPicService.lambdaQuery()
                .eq(WeighDataPicDO::getFilePath, form.getFilePath())
                .eq(WeighDataPicDO::getUid, dto.getUid())
                .list();
        if (CollUtil.isNotEmpty(list)) {
            return;
        }
        WeighDataPicDO weighDataPicDO = new WeighDataPicDO();
        BeanUtils.copyProperties(form, weighDataPicDO);
        weighDataPicDO.setRecordId(form.getWeighDataId());
        weighDataPicDO.setUid(dto.getUid());
        weighDataPicDO.setAttributionId(dto.getAttributionId());
        if (form.getFilePath().contains("1#") || form.getFilePath().contains("2#")) {
            weighDataPicDO.setType(1);
        }
        weighDataPicService.save(weighDataPicDO);
        // 异步推送数据
        weighDataPushBusinessService.syncPushWeighDataPic(weighDataPicDO);
    }

    /**
     * 校验
     */
    @Override
    public WeighDataCheckDTO check(String deviceSn, Long appId, boolean isOssUpload, String deviceType) {
        WeighDataCheckDTO dto = deviceExtMapper.selectInfoByDeviceSn(deviceSn, appId, deviceType);
        // 设备是否准入
        if (ObjectUtil.isNull(dto)) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_EXIST);
        }
        // 设备是否启用
        if (dto.getIsUsed().equals(DeviceIsUsedEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_USED);
        }
        // 设备是否占有
        if (StrUtil.isBlank(dto.getUid())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_HAS_NO_OCCUPIED);
        }
        // 设备是否绑定
        if (dto.getAttributionId() == null) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_NOT_HAVE_ATTRIBUTION);
        }
        // 设备是否接收
        if (dto.getReceive().equals(DeviceReceiveEnum.STOP.value())) {
            throw new BizErrorException(BizExceptionMessageEnum.DEVICE_IS_NOT_RECEIVE);
        }
        // 服务是否启用
        if (dto.getType().equals(DeveloperTypeEnum.STOP.value())) {
            throw new BizErrorException("-21", DeveloperAppEnum.descByType(appId) + "服务已停用");
        }
        // 服务是否欠费
//        if (dto.getState().equals(DeveloperStatusEnum.STOP.value())) {
//            throw new BizErrorException(BizExceptionMessageEnum.APP_IS_TIMEOUT);
//        }

        // 服务调用次数 排除oss上传
        if (!isOssUpload) {
            if (userConfigService.getApiRemainingCount(dto.getUid()) <= 0) {
                throw new BizErrorException(BizExceptionMessageEnum.APP_INVOKE_EMPTY);
            }
        }
        // 空间是否使用完
        if (NumberUtil.isGreaterOrEqual(dto.getSpaceUseSize(), dto.getSpaceSize())) {
            throw new BizErrorException(BizExceptionMessageEnum.APP_SPACE_EMPTY);
        }

        // 空间是否已到期
        if (dto.getSpaceExpire().isBefore(LocalDateTime.now())) {
            throw new BizErrorException(BizExceptionMessageEnum.APP_SPACE_END);
        }
        return dto;
    }

    @Override
    public List<WeighDataPicVO> picList(String weighDataId) {
        List<WeighDataPicVO> result = new ArrayList<>();
        List<WeighDataPicDO> list = weighDataPicService.lambdaQuery()
                .eq(WeighDataPicDO::getRecordId, weighDataId)
                .select(WeighDataPicDO::getFileId)
                .groupBy(WeighDataPicDO::getFilePath)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            result = list.stream().map(e -> {
                WeighDataPicVO vo = new WeighDataPicVO();
                BeanUtils.copyProperties(e, vo);
                vo.setUrl(fileOssService.getUrlByUuid(vo.getFileId()));
                return vo;
            }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public DataSyncVO sync(String deviceSn, String deviceType) {
        return deviceExtMapper.syncInfo(deviceSn, deviceType);
    }

    @Override
    public WeighDataVO card(String weighDataId) {
        WeighDataVO vo = weighDataExtMapper.card(weighDataId);
        if (ObjectUtil.isNotNull(vo)) {
            vo.setTypeStr(WeighDataTypeEnum.desc(vo.getType()));
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRiskGrade(Long id, String riskGrade) {
        if (StringUtils.isBlank(riskGrade)) {
            throw new BizErrorException(BizExceptionMessageEnum.RISK_GRADE_EMPTY);
        }
        boolean validate = RiskGradeEnum.validate(riskGrade);
        if (!validate) {
            throw new BizErrorException(BizExceptionMessageEnum.RISK_GRADE_ILLEGALITY);
        }
        WeighDataDO dataDO = this.getById(id);
        String oldRiskGrade = Optional.ofNullable(dataDO.getRiskGrade()).orElse("");
        if (oldRiskGrade.equals(riskGrade)) {
            throw new BizErrorException(BizExceptionMessageEnum.RISK_GRADE_ERROR);
        }
        WeighDataDO entity = new WeighDataDO();
        entity.setId(id);
        entity.setRiskGrade(riskGrade);
        this.updateById(entity);

        // 操作日志
        weighDataUpdateLog(id, WeighDataUpdateTypeEnum.RISK_GRADE.name(), oldRiskGrade, riskGrade);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateRiskGradeTask(Long id, String riskGrade, String email) {
        WeighDataDO dataDO = this.getById(id);
        String oldRiskGrade = Optional.ofNullable(dataDO.getRiskGrade()).orElse("");
        this.lambdaUpdate().eq(WeighDataDO::getId, id).set(WeighDataDO::getRiskGrade, riskGrade).update();
        weighDataLog(id, WeighDataUpdateTypeEnum.RISK_GRADE.name(), oldRiskGrade, riskGrade, email);
    }

    @Override
    public void updatePushStatus(List<String> recordIdList) {
        if (CollUtil.isNotEmpty(recordIdList)) {
            weighDataService.lambdaUpdate()
                    .in(WeighDataDO::getRecordId, recordIdList)
                    .set(WeighDataDO::getPushStatus, PushStatusEnum.NONPUSH.getVal())
                    .set(WeighDataDO::getWaitTime, null)
                    .update();

            weighDataPicService.lambdaUpdate()
                    .in(WeighDataPicDO::getRecordId, recordIdList)
                    .set(WeighDataPicDO::getPushStatus, PushStatusEnum.NONPUSH.getVal())
                    .set(WeighDataPicDO::getWaitTime, null)
                    .update();
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateTruckNo(Long id, String truckNo, Boolean flag) {
        if (StringUtils.isBlank(truckNo.trim())) {
            throw new BizErrorException(BizExceptionMessageEnum.TRUCK_NO_EMPTY);
        }
        WeighDataDO dataDO = this.getById(id);
        String oldTruckNo = Optional.ofNullable(dataDO.getTruckNo()).orElse("");
        if (oldTruckNo.equals(truckNo)) {
            throw new BizErrorException(BizExceptionMessageEnum.TRUCK_NO_ERROR);
        }

        WeighDataDO entity = new WeighDataDO();
        entity.setId(id);
        entity.setTruckNo(truckNo);
        if (flag) {
            entity.setLprTruckNo(truckNo);
        }
        this.updateById(entity);

        // 操作日志
        weighDataUpdateLog(id, WeighDataUpdateTypeEnum.TRUCK_NO.name(), oldTruckNo, truckNo);
        if (flag) {
            weighDataUpdateLog(id, WeighDataUpdateTypeEnum.LPR_TRUCK_NO.name(),
                    Optional.ofNullable(dataDO.getLprTruckNo()).orElse(""), truckNo);
        }
    }

    @Override
    public List<WeighDataUpdateLogVO> updateLog(Long id) {
        List<WeighDataUpdateLogVO> list = Lists.newArrayList();

        QueryWrapper<WeighDataUpdateLogDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WeighDataUpdateLogDO::getWeighDataId, id).orderByDesc(WeighDataUpdateLogDO::getGmtCreate);
        List<WeighDataUpdateLogDO> weighDataUpdateLogDOS = weighDataUpdateLogMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(weighDataUpdateLogDOS)) {
            list = weighDataUpdateLogDOS.stream().map(item -> {
                WeighDataUpdateLogVO logVO = new WeighDataUpdateLogVO();
                logVO.setUpdateType(WeighDataUpdateTypeEnum.valueOf(item.getUpdateType()).desc());
                logVO.setOldValue(item.getOldValue());
                logVO.setNewValue(item.getNewValue());
                logVO.setEmail(item.getEmail());
                logVO.setGmtCreate(item.getGmtCreate());
                return logVO;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public void weighDataUpdateLog(Long id, String updateType, String oldValue, String newValue) {
        UserVO userVO = userService.currentUserByType();
        WeighDataUpdateLogDO updateLog = new WeighDataUpdateLogDO();
        updateLog.setWeighDataId(id);
        updateLog.setUpdateType(updateType);
        updateLog.setOldValue(oldValue);
        updateLog.setNewValue(newValue);
        if (userVO != null) {
            updateLog.setEmail(userVO.getIsFake() ? ConfigConstant.MANAGER : userVO.getEmail());
        }
        weighDataUpdateLogMapper.insert(updateLog);
    }

    @Override
    public void weighDataLog(Long id, String updateType, String oldValue, String newValue, String email) {
        if (StrUtil.equals(oldValue, newValue)) {
            return;
        }
        WeighDataUpdateLogDO updateLog = new WeighDataUpdateLogDO();
        updateLog.setWeighDataId(id);
        updateLog.setUpdateType(updateType);
        updateLog.setOldValue(StrUtil.isBlank(oldValue) ? "" : oldValue);
        updateLog.setNewValue(StrUtil.isBlank(newValue) ? "" : newValue);
        updateLog.setCreateId(email);
        updateLog.setModifyId(email);
        updateLog.setEmail(email);
        weighDataUpdateLogMapper.insert(updateLog);
    }

    @Override
    public List<ExpirePicFixForm> expirePicFix(List<ExpirePicFixForm> list) {
        if (CollUtil.isNotEmpty(list)) {
            List<String> records = new ArrayList<>();
            list.forEach(e -> {
                records.add(e.getRecord1());
                records.add(e.getRecord2());
            });

            List<ExpirePicFixDTO> dtos = weighDataExtMapper.expirePicFix(records.stream().distinct().collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(dtos)) {
                Map<String, List<ExpirePicFixDTO>> map = dtos.stream().collect(Collectors.groupingBy(ExpirePicFixDTO::getRecordId));
                Map<String, ExpirePicFixDTO> dtoMap = new HashMap<>();
                map.forEach((k, v) -> {
                    ExpirePicFixDTO expirePicFixDTO = new ExpirePicFixDTO();
                    LocalDateTime weighTime = v.get(0).getWeighTime();
                    expirePicFixDTO.setRecordId(k);
                    expirePicFixDTO.setWeighTime(weighTime);
                    String pic = v.stream().map(ExpirePicFixDTO::getFileId).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                    expirePicFixDTO.setFileId(StrUtil.isNotBlank(pic) ? pic : null);
                    dtoMap.put(k, expirePicFixDTO);
                });


                list.forEach(e -> {
                    ExpirePicFixDTO record1 = dtoMap.get(e.getRecord1());
                    ExpirePicFixDTO record2 = dtoMap.get(e.getRecord2());

                    if (record1.getWeighTime().isBefore(record2.getWeighTime())) {
                        e.setEnterPic(record1.getFileId());
                        e.setLeavePic(record2.getFileId());
                    } else {
                        e.setEnterPic(record2.getFileId());
                        e.setLeavePic(record1.getFileId());
                    }
                });

                return list;
            }
        }

        return null;
    }

    @Override
    public RefreshPicVO getPicByTwoRecordId(RefreshPicForm form) {
        if (ObjectUtil.isNull(form) || StrUtil.isBlank(form.getRecord1()) || StrUtil.isBlank(form.getRecord2())) {
            return null;
        }

        List<String> recordIdList = new ArrayList<>();
        recordIdList.add(form.getRecord1());
        recordIdList.add(form.getRecord2());

        List<ExpirePicFixDTO> dtos = weighDataExtMapper.expirePicFix(recordIdList);
        if (CollUtil.isNotEmpty(dtos)) {
            Map<String, List<ExpirePicFixDTO>> map = dtos.stream().collect(Collectors.groupingBy(ExpirePicFixDTO::getRecordId));
            Map<String, ExpirePicFixDTO> dtoMap = new HashMap<>();
            map.forEach((k, v) -> {
                ExpirePicFixDTO expirePicFixDTO = new ExpirePicFixDTO();
                LocalDateTime weighTime = v.get(0).getWeighTime();
                expirePicFixDTO.setRecordId(k);
                expirePicFixDTO.setWeighTime(weighTime);
                List<String> collect = v.stream().map(ExpirePicFixDTO::getFileId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                Map<String, String> picMap = fileCenterService.simpleAcquireFileDownloadUrls(collect);
                if (CollUtil.isNotEmpty(picMap)) {
                    String picStr = String.join(",", picMap.values());
                    expirePicFixDTO.setFileId(StrUtil.isNotBlank(picStr) ? picStr : null);
                    dtoMap.put(k, expirePicFixDTO);
                }
            });

            if (dtoMap.keySet().size() == 2) {
                RefreshPicVO vo = new RefreshPicVO();

                vo.setRecord1(form.getRecord1());
                vo.setRecord2(form.getRecord2());

                ExpirePicFixDTO record1 = dtoMap.get(form.getRecord1());
                ExpirePicFixDTO record2 = dtoMap.get(form.getRecord2());

                if (record1.getWeighTime().isBefore(record2.getWeighTime())) {
                    vo.setEnterPic(record1.getFileId());
                    vo.setLeavePic(record2.getFileId());
                } else {
                    vo.setEnterPic(record2.getFileId());
                    vo.setLeavePic(record1.getFileId());
                }
                return vo;
            }
        }
        return null;
    }

    @Override
    public List<WeighDataSimpleVO> getWeighDataListByIdS(List<String> recordIdList) {
        List<WeighDataSimpleVO> result = new ArrayList<>();
        if (CollUtil.isEmpty(recordIdList)) {
            return result;
        }

        List<WeighDataDO> weighDataDOList = weighDataService.lambdaQuery()
                .in(WeighDataDO::getRecordId, recordIdList)
                .list();
        List<WeighDataPicDO> weighDataPicDOList = weighDataPicService.lambdaQuery()
                .in(WeighDataPicDO::getRecordId, recordIdList)
                .list();
        if (CollUtil.isEmpty(weighDataDOList)) {
            return result;
        }
        Map<String, List<WeighDataPicDO>> dataPicMap = new HashMap<>();
        Map<String, String> picMap = new HashMap<>();
        if (CollUtil.isNotEmpty(weighDataPicDOList)) {
            List<String> picAll = weighDataPicDOList.stream().map(WeighDataPicDO::getFileId).distinct().collect(Collectors.toList());
            picMap = fileCenterService.simpleAcquireFileDownloadUrls(picAll);
            dataPicMap = weighDataPicDOList.stream().collect(Collectors.groupingBy(WeighDataPicDO::getRecordId));
        }

        Map<String, List<WeighDataPicDO>> finalDataPicMap = dataPicMap;
        Map<String, String> finalPicMap = picMap;
        result = weighDataDOList.stream().map(e -> {
            WeighDataSimpleVO vo = new WeighDataSimpleVO();
            BeanUtils.copyProperties(e, vo);

            if (CollUtil.isNotEmpty(finalDataPicMap) && CollUtil.isNotEmpty(finalDataPicMap.get(e.getRecordId())) && CollUtil.isNotEmpty(finalPicMap)) {
                List<String> pic = finalDataPicMap.get(e.getRecordId()).stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                String picStr = pic.stream().map(finalPicMap::get).collect(Collectors.joining(","));
                vo.setPic(picStr);
            }
            return vo;
        }).collect(Collectors.toList());

        return result;
    }

    @Override
    public PageList<WeighDataConfirmDetailDTO> dataQuery(WeighDataQuery query) {
        UserDO userDO = new UserDO();
        if (StringUtils.isNotBlank(query.getAppKey())) {
            userDO = userService.getUserByAppKey(query.getAppKey());
        } else {
            String uid = query.getUid();
            userDO = userService.getByUid(uid);
        }
        WeighDataLocalQuery localQuery = new WeighDataLocalQuery();
        BeanUtils.copyProperties(query, localQuery);
        localQuery.setCurrent(1);
        localQuery.setSize(SdkQueryConstant.DATA_MAX_SIZE);
        IPage<WeighDataConfirmDetailDTO> page = weighDataExtMapper.dataQuery(userDO.getUid(), localQuery);
        PageList<WeighDataConfirmDetailDTO> result = new PageList<>();
        BeanUtils.copyProperties(page, result);
        result.setDataList(page.getRecords());
        return result;
    }

    @Override
    public IPage<WeighDataPullVO> getDataPull(WeighPullQuery query) {
        IPage<WeighDataPullDTO> page = weighDataExtMapper.getDataPull(query);
        IPage<WeighDataPullVO> result = new Page<>();

        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighDataPullDTO> dataList = page.getRecords();
            Map<String, String> weighDataIdPicMap = new HashMap<>();
            Map<String, WeighDataCurvePullVO> weighCurveMap = new HashMap<>();

            List<String> recordIdList = dataList.stream().map(WeighDataPullDTO::getRecordId).collect(Collectors.toList());
            List<WeighDataPicDO> picList = weighDataPicService.lambdaQuery()
                    .in(WeighDataPicDO::getRecordId, recordIdList)
                    .list();
            if (CollUtil.isNotEmpty(picList)) {
                List<String> uuidList = picList.stream().map(WeighDataPicDO::getFileId).collect(Collectors.toList());
                DownloadUrlOptionsDto options = new DownloadUrlOptionsDto();
                options.setTime(Date.from(LocalDate.now().plusYears(10).atStartOfDay(ZoneId.systemDefault()).toInstant()));
                List<FileIdentityDto> fileIdentities = uuidList.stream().map(e -> {
                    FileIdentityDto dto = new FileIdentityDto();
                    dto.setFileUuid(e);
                    return dto;
                }).collect(Collectors.toList());
                Map<FileIdentityDto, String> downloadDtoMap = fileCenterService.acquireFileDownloadUrls(fileIdentities, options);
                if (CollUtil.isNotEmpty(downloadDtoMap)) {
                    Map<String, String> downloadMap = new HashMap<>();
                    downloadDtoMap.forEach((k, v) -> downloadMap.put(k.getFileUuid(), v));
                    picList.forEach(data -> {
                        if (StrUtil.isNotBlank(downloadMap.get(data.getFileId()))) {
                            if (weighDataIdPicMap.containsKey(data.getRecordId())) {
                                String pic = weighDataIdPicMap.get(data.getRecordId());
                                weighDataIdPicMap.put(data.getRecordId(), pic + "," + downloadMap.get(data.getFileId()));
                            } else {
                                weighDataIdPicMap.put(data.getRecordId(), downloadMap.get(data.getFileId()));
                            }
                        }
                    });
                }

            }

            if (query.getCurveFlag() != null && query.getCurveFlag()) {
                List<WeighCurveDO> curveList = weighCurveService.lambdaQuery()
                        .in(WeighCurveDO::getRecordId, recordIdList)
                        .list();
                if (CollUtil.isNotEmpty(curveList)) {
                    curveList.forEach(data -> {
                        List<Double> weights = new ArrayList<>();
                        List<LocalDateTime> times = new ArrayList<>();
                        List<WeighDataSavedDTO> points = new ArrayList<>();
                        try {
                            weighCurveServiceImpl.handle(weights, times, points, data.getWeighCurveData(), data.getCreateTime());
                        } catch (JsonProcessingException e) {
                            log.info("获取称重曲线出错,recordId:{}", data.getRecordId());
                        }
                        WeighDataCurvePullVO curvePullVO = new WeighDataCurvePullVO();
                        curvePullVO.setWeights(weights);
                        curvePullVO.setTimes(times);
                        curvePullVO.setPoints(points);
                        weighCurveMap.put(data.getRecordId(), curvePullVO);
                    });
                }
            }

            List<WeighDataPullVO> collect = dataList.stream().map(e -> {
                WeighDataPullVO vo = new WeighDataPullVO();
                BeanUtils.copyProperties(e, vo);
                if (CollUtil.isNotEmpty(weighDataIdPicMap)) {
                    vo.setPic(weighDataIdPicMap.get(vo.getRecordId()));
                }
                if (CollUtil.isNotEmpty(weighCurveMap)) {
                    vo.setCurve(weighCurveMap.get(vo.getRecordId()));
                }
                return vo;
            }).collect(Collectors.toList());

            BeanUtils.copyProperties(page, result);
            result.setRecords(collect);
        }
        return result;
    }

    @Override
    public void updateCheatAlarmId(String recordId, String cheatAlarmId) {
        WeighDataDO weighDataDO = this.lambdaQuery()
                .eq(WeighDataDO::getRecordId, recordId)
                .one();
        if (Objects.nonNull(weighDataDO)) {
            weighDataDO.setCheatAlarmId(cheatAlarmId);
            this.updateById(weighDataDO);
        }
    }

    @Override
    public void grossTareExport(WeighDataPageQuery query, HttpServletResponse response) {
        // 使用称重确认单数据进行导出
        query.setCurrent(1L);
        query.setSize(Integer.MAX_VALUE);
        IPage<WeighDataConfirmVO> page = weighDataConfirmService.col(query);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<WeighDataGrossTareExportDTO> result = page.getRecords().stream().map(vo -> {
                WeighDataGrossTareExportDTO dto = new WeighDataGrossTareExportDTO();
                BeanUtil.copyProperties(vo, dto);
                // 转换称重类型
                if (Objects.nonNull(vo.getWeighType())) {
                    dto.setWeighTypeStr(vo.getWeighType() == 1 ? "收料过磅" : "发料过磅");
                }
                // 转换推送状态
                if (Objects.nonNull(vo.getPushState())) {
                    dto.setPushStateStr(PushStatusEnum.KEY_MAP.getOrDefault(vo.getPushState().byteValue(), "未知"));
                }
                // 转换进场时间
                if (Objects.nonNull(vo.getEnterTime())) {
                    dto.setEnterTimeStr(LocalDateTimeUtil.format(vo.getEnterTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                // 转换出场时间
                if (Objects.nonNull(vo.getLeaveTime())) {
                    dto.setLeaveTimeStr(LocalDateTimeUtil.format(vo.getLeaveTime(), DatePattern.NORM_DATETIME_PATTERN));
                }
                // 转换创建时间
                if (StrUtil.isNotBlank(vo.getGmtCreate())) {
                    dto.setGmtCreateStr(vo.getGmtCreate());
                }
                return dto;
            }).collect(Collectors.toList());
            ExcelUtil.export(response, result, "仅毛皮重称重记录");
        }
    }
}
