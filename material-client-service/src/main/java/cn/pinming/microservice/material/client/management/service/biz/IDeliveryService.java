package cn.pinming.microservice.material.client.management.service.biz;

import cn.pinming.microservice.material.client.management.common.form.DeliveryAddForm;
import cn.pinming.microservice.material.client.management.common.form.DeliveryAddSelfCheckForm;
import cn.pinming.microservice.material.client.management.common.form.DeliverySyncForm;
import cn.pinming.microservice.material.client.management.common.model.DeliveryDO;
import cn.pinming.microservice.material.client.management.common.model.ext.WeighDataConfirmOriginExtDO;
import cn.pinming.microservice.material.client.management.common.query.DeliveryPageQuery;
import cn.pinming.microservice.material.client.management.common.query.DeliveryQuery;
import cn.pinming.microservice.material.client.management.common.query.SelfCheckQuery;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryDetailDTO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryTruckVO;
import cn.pinming.microservice.material.client.management.common.vo.delivery.DeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.h5.SimpleDeliveryVO;
import cn.pinming.microservice.material.client.management.common.vo.selfcheck.SelfCheckDeliveryVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 发货单(运单) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface IDeliveryService extends IService<DeliveryDO> {

    List<DeliveryTruckVO> selectTruckList();

    Long add(DeliveryAddForm form);

    Page<DeliveryVO> pageByQuery(DeliveryPageQuery query);

    void cancel(Long deliveryId);

    DeliveryDetailDTO detailByDeliveryId(Long deliveryId);

    SelfCheckDeliveryVO getDeliveryDetail(SelfCheckQuery query);

    Long h5Add(DeliveryAddSelfCheckForm form);

    String selfCheckAdd(DeliveryAddSelfCheckForm form);

    SimpleDeliveryVO h5DetailByDeliveryId(Long deliveryId);

    void sendDeliverySms(Long deliveryId);

    String deliveryAdd(Long deliveryAdd, Integer type,String truckNo);

    String selfCheckOCRAdd(String deviceSn, String deviceType);

    String selfCheckWeighAdd(String deviceSn, String deviceType,String truckNo);

    void updateDriverMobile(Long deliveryId, String mobile);

    String deliveryAdd(DeliverySyncForm form, String appKeyHeader);

    WeighDataConfirmOriginExtDO delivery(DeliveryQuery query);

    boolean checkAllowWeighing(DeliveryQuery query);

    void listExport(DeliveryPageQuery query, HttpServletResponse response);
}
