package cn.pinming.microservice.material.client.management.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 称重确认单推送状态枚举
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
public enum ConfirmPushStateEnum {
    UNPUSHED(1, "未推送"),
    PUSHED(2, "已推送"),
    PUSH_FAILED(3, "推送失败"),
    ;

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> VALUE_DESC_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ConfirmPushStateEnum::getValue, ConfirmPushStateEnum::getDesc));

    ConfirmPushStateEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据值获取描述
     *
     * @param value 状态值
     * @return 状态描述
     */
    public static String getDescByValue(Integer value) {
        return VALUE_DESC_MAP.getOrDefault(value, "未知");
    }
}
